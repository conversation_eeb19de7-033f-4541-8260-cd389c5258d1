const puppeteer = require('puppeteer');
const StealthPlugin = require('puppeteer-extra-plugin-stealth');
const puppeteerExtra = require('puppeteer-extra');
const fs = require('fs');
const path = require('path');
const TokenHandler = require('../token-api/token.js');
const CaptchaHandler = require('./captcha.js');
const ProxyHandler = require('./proxy.js');
const EmailHandler = require('./email.js');
const OneMailHandler = require('./onemail.js');
const AntiFingerprint = require('./anti-fingerprint-config.js');
const Logger = require('./logger.js');

puppeteerExtra.use(StealthPlugin());

class AutoRegister {
    constructor() {
        this.browser = null;
        this.page = null;
        this.authToken = null;
        this.baseUrl = 'http://127.0.0.1:3000（augment2api运行的ip加端口，nat机填本地IP';
        this.tempEmail = null;
        this.stepCounter = 0;
        this.currentProxy = null;
        this.networkResponses = [];

        this.tokenHandler = new TokenHandler();
        this.captchaHandler = new CaptchaHandler();
        this.proxyHandler = new ProxyHandler();
        this.emailHandler = new EmailHandler();
        this.oneMailHandler = new OneMailHandler();
        this.logger = new Logger();

        this.imageDir = path.join(__dirname, 'image');
        if (!fs.existsSync(this.imageDir)) {
            fs.mkdirSync(this.imageDir, { recursive: true });
        }
    }

    log(message) {
        console.log(`[${new Date().toLocaleTimeString()}] ${message}`);
    }

    // 启用高分数reCAPTCHA模式
    enableHighScoreCaptchaMode() {
        this.captchaHandler.enableHighScoreMode();
        this.log('🎯 已启用高分数reCAPTCHA模式');
    }

    // 禁用高分数reCAPTCHA模式
    disableHighScoreCaptchaMode() {
        this.captchaHandler.disableHighScoreMode();
        this.log('🎯 已禁用高分数reCAPTCHA模式');
    }

    async takeScreenshot(stepName, isError = false) {
        try {
            this.stepCounter++;
            const prefix = isError ? 'ERROR' : 'STEP';
            const filename = `${prefix}_${this.stepCounter.toString().padStart(2, '0')}_${stepName}.png`;
            const filepath = path.join(this.imageDir, filename);
            await this.page.screenshot({ path: filepath, fullPage: true });
            this.log(`📸 ${filename}`);
        } catch (error) { }
    }

    async saveHtmlContent(stepName, isError = false) {
        try {
            const prefix = isError ? 'ERROR' : 'STEP';
            const filename = `${prefix}_${this.stepCounter.toString().padStart(2, '0')}_${stepName}.html`;
            const filepath = path.join(this.imageDir, filename);
            const htmlContent = await this.page.content();
            fs.writeFileSync(filepath, htmlContent, 'utf8');
            this.log(`💾 ${filename}`);
        } catch (error) {
            this.log(`保存HTML失败: ${error.message}`);
        }
    }

    async wait(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    // 模拟真人页面探索行为
    async simulateHumanPageExploration() {
        try {
            this.log('🔍 模拟真人页面探索行为...');

            // 随机滚动页面（模拟查看内容）
            const scrollActions = Math.floor(Math.random() * 3) + 1; // 1-3次滚动
            for (let i = 0; i < scrollActions; i++) {
                const scrollY = Math.floor(Math.random() * 300) + 100; // 100-400px
                await this.page.evaluate((y) => {
                    window.scrollBy(0, y);
                }, scrollY);
                await this.wait(AntiFingerprint.randomBetween(300, 800));

                // 有时候滚回去一点（模拟真人的回看行为）
                if (Math.random() < 0.3) {
                    await this.page.evaluate((y) => {
                        window.scrollBy(0, -y * 0.3);
                    }, scrollY);
                    await this.wait(AntiFingerprint.randomBetween(200, 500));
                }
            }

            // 随机鼠标移动（模拟无意识的鼠标移动）
            await this.simulateRandomMouseMovement();

        } catch (error) {
            this.log(`页面探索模拟出错: ${error.message}`);
        }
    }

    // 模拟随机鼠标移动
    async simulateRandomMouseMovement() {
        try {
            const viewport = await this.page.viewport();
            const movements = Math.floor(Math.random() * 3) + 1; // 1-3次移动

            for (let i = 0; i < movements; i++) {
                const x = Math.floor(Math.random() * viewport.width * 0.8) + viewport.width * 0.1;
                const y = Math.floor(Math.random() * viewport.height * 0.8) + viewport.height * 0.1;

                await this.page.mouse.move(x, y, {
                    steps: Math.floor(Math.random() * 10) + 5 // 5-15步，模拟真实鼠标轨迹
                });
                await this.wait(AntiFingerprint.randomBetween(100, 400));
            }
        } catch (error) {
            this.log(`鼠标移动模拟出错: ${error.message}`);
        }
    }

    // 模拟犹豫行为（真人的思考时间）
    async simulateHesitation() {
        try {
            this.log('🤔 模拟思考犹豫...');

            // 随机的犹豫时间
            const hesitationTime = AntiFingerprint.randomBetween(800, 2000);
            await this.wait(hesitationTime);

            // 有时候会有小的鼠标移动（模拟不确定的手势）
            if (Math.random() < 0.4) {
                const viewport = await this.page.viewport();
                const currentPos = await this.page.evaluate(() => {
                    return { x: window.innerWidth / 2, y: window.innerHeight / 2 };
                });

                // 小范围的鼠标移动
                const deltaX = (Math.random() - 0.5) * 50;
                const deltaY = (Math.random() - 0.5) * 50;

                await this.page.mouse.move(
                    currentPos.x + deltaX,
                    currentPos.y + deltaY,
                    { steps: 3 }
                );
                await this.wait(AntiFingerprint.randomBetween(200, 500));
            }
        } catch (error) {
            this.log(`犹豫行为模拟出错: ${error.message}`);
        }
    }

    // 模拟焦点切换行为
    async simulateFocusChange() {
        try {
            if (Math.random() < 0.3) { // 30%概率执行
                this.log('👁️ 模拟焦点切换...');

                // 按Tab键切换焦点
                await this.page.keyboard.press('Tab');
                await this.wait(AntiFingerprint.randomBetween(200, 600));

                // 有时候再按一次
                if (Math.random() < 0.5) {
                    await this.page.keyboard.press('Tab');
                    await this.wait(AntiFingerprint.randomBetween(200, 400));
                }
            }
        } catch (error) {
            this.log(`焦点切换模拟出错: ${error.message}`);
        }
    }

    // 模拟验证成功后的自然反应
    async simulateVerificationSuccessReaction() {
        try {
            this.log('✅ 模拟验证成功后的自然反应...');

            // 短暂停顿（模拟确认验证成功）
            await this.wait(AntiFingerprint.randomBetween(1000, 2000));

            // 轻微的页面探索（查看结果）
            await this.simulateHumanPageExploration();

            // 犹豫一下（模拟思考下一步）
            await this.simulateHesitation();

        } catch (error) {
            this.log(`验证成功反应模拟出错: ${error.message}`);
        }
    }

    async saveDebugInfo(filename, data) {
        try {
            const debugDir = path.join(__dirname, 'debug');
            if (!fs.existsSync(debugDir)) {
                fs.mkdirSync(debugDir, { recursive: true });
            }

            const filepath = path.join(debugDir, filename);
            const jsonData = JSON.stringify(data, null, 2);
            fs.writeFileSync(filepath, jsonData, 'utf8');
            this.log(`💾 调试信息已保存: ${filename}`);
        } catch (error) {
            this.log(`保存调试信息失败: ${error.message}`);
        }
    }

    async detectCaptcha() {
        try {
            const captchaInfo = await this.page.evaluate(() => {
                const result = {
                    hasTurnstile: false,
                    hasRecaptcha: false,
                    siteKey: null,
                    currentUrl: window.location.href
                };

                const turnstileIframes = document.querySelectorAll('iframe[src*="challenges.cloudflare.com"]');
                if (turnstileIframes.length > 0) {
                    result.hasTurnstile = true;
                    const src = turnstileIframes[0].src;
                    const match = src.match(/0x4[A-Za-z0-9]{20,}/);
                    if (match) result.siteKey = match[0];
                }

                const siteKeyElements = document.querySelectorAll('[data-sitekey]');
                if (siteKeyElements.length > 0) {
                    const sitekey = siteKeyElements[0].getAttribute('data-sitekey');
                    if (sitekey && sitekey.startsWith('0x4')) {
                        result.hasTurnstile = true;
                        result.siteKey = sitekey;
                    }
                }

                if (window.grecaptcha || document.querySelector('.g-recaptcha') ||
                    document.querySelector('[data-sitekey*="6L"]') ||
                    document.querySelector('#g-recaptcha-response') ||
                    document.body.innerHTML.includes('grecaptcha.enterprise.execute')) {
                    result.hasRecaptcha = true;
                }

                if (!result.siteKey) {
                    const pageContent = document.documentElement.outerHTML;
                    const match = pageContent.match(/0x4[A-Za-z0-9]{20,}/);
                    if (match) {
                        result.siteKey = match[0];
                        result.hasTurnstile = true;
                    }
                }

                return result;
            });

            return captchaInfo;
        } catch (error) {
            return { hasTurnstile: false, hasRecaptcha: false, siteKey: null, currentUrl: this.page.url() };
        }
    }

    async handleCaptchaIfPresent() {
        const captchaInfo = await this.detectCaptcha();

        if (!captchaInfo.hasTurnstile && !captchaInfo.hasRecaptcha) {
            this.log('未检测到验证码');
            return true;
        }

        this.log('检测到验证码');
        await this.takeScreenshot('captcha_detected');

        // 调试模式：使用YesCaptcha获取token并保存调试信息
        this.log('🔍 开始调试验证码状态...');

        // 先检查初始状态
        const initialStatus = await this.checkCaptchaStatus();
        this.log(`初始验证码状态: ${JSON.stringify(initialStatus, null, 2)}`);

        // 保存初始状态到文件
        await this.saveDebugInfo('initial_captcha_status.json', initialStatus);

        let success = false;
        let debugData = {
            initialStatus,
            yescaptchaUsed: false,
            token: null,
            injectionResult: null,
            finalStatus: null,
            screenshots: [],
            timestamp: new Date().toISOString()
        };

        if (captchaInfo.hasTurnstile && captchaInfo.siteKey) {
            this.log('🎯 使用YesCaptcha处理Turnstile验证码...');
            debugData.yescaptchaUsed = true;

            success = await this.captchaHandler.handleTurnstile(this.page, captchaInfo.currentUrl, captchaInfo.siteKey);

            if (success) {
                this.log('✅ YesCaptcha返回成功');
                await this.takeScreenshot('yescaptcha_success');
                debugData.screenshots.push('yescaptcha_success.png');

                // 等待一段时间让页面处理token
                await this.wait(5000);

                // 检查token注入后的状态
                const afterTokenStatus = await this.checkCaptchaStatus();
                this.log(`Token注入后状态: ${JSON.stringify(afterTokenStatus, null, 2)}`);
                debugData.finalStatus = afterTokenStatus;

                await this.takeScreenshot('after_token_injection');
                debugData.screenshots.push('after_token_injection.png');

            } else {
                this.log('❌ YesCaptcha处理失败');
                await this.takeScreenshot('yescaptcha_failed', true);
                debugData.screenshots.push('yescaptcha_failed.png');
            }
        }

        if (captchaInfo.hasRecaptcha) {
            this.log('🎯 处理reCAPTCHA...');
            const recaptchaSuccess = await this.captchaHandler.handleRecaptchaEnterprise(this.page);
            success = success || recaptchaSuccess;

            // 如果reCAPTCHA处理成功，检查是否为自动提交页面
            if (recaptchaSuccess) {
                // 检查当前URL和页面内容
                const currentUrl = this.page.url();
                this.log(`🔍 reCAPTCHA处理成功后的URL: ${currentUrl}`);

                if (currentUrl !== captchaInfo.currentUrl) {
                    this.log('🔍 检测到页面已跳转，检查跳转结果...');

                    // 检查页面内容判断是成功还是失败
                    const pageResult = await this.page.evaluate(() => {
                        const bodyText = document.body.innerText.toLowerCase();
                        const bodyHtml = document.body.innerHTML.toLowerCase();

                        // 检查失败标识
                        const failureIndicators = [
                            'rejected', 'failed', 'error', 'denied', 'blocked',
                            'try again', 'unsuccessful', 'invalid', 'expired'
                        ];

                        // 检查成功标识
                        const successIndicators = [
                            'success', 'complete', 'authorized', 'approved',
                            'verified', 'welcome', 'dashboard', 'copy'
                        ];

                        const hasFailure = failureIndicators.some(indicator =>
                            bodyText.includes(indicator) || bodyHtml.includes(indicator)
                        );

                        const hasSuccess = successIndicators.some(indicator =>
                            bodyText.includes(indicator) || bodyHtml.includes(indicator)
                        );

                        return {
                            hasFailure,
                            hasSuccess,
                            bodyText: bodyText.substring(0, 500), // 前500字符用于调试
                            url: window.location.href
                        };
                    });

                    this.log(`📄 页面内容分析: 失败标识=${pageResult.hasFailure}, 成功标识=${pageResult.hasSuccess}`);
                    this.log(`📝 页面文本预览: ${pageResult.bodyText}`);

                    if (pageResult.hasFailure) {
                        this.log('❌ 检测到失败页面，reCAPTCHA处理虽成功但注册被拒绝');
                        await this.takeScreenshot('recaptcha_success_but_rejected');
                        return false; // 返回失败
                    } else if (pageResult.hasSuccess) {
                        this.log('✅ 检测到成功页面，reCAPTCHA处理成功');
                        await this.takeScreenshot('recaptcha_success_redirected');
                        return true; // 返回成功
                    } else {
                        this.log('⚠️ 页面跳转但无法确定结果，继续检查验证码状态');
                        await this.takeScreenshot('recaptcha_success_unknown_result');
                    }
                }
            }
        }

        // 最终状态检查 - 只在页面未跳转时进行
        const finalStatus = await this.checkCaptchaStatus();
        debugData.finalStatus = finalStatus;
        this.log(`最终验证码状态: ${JSON.stringify(finalStatus, null, 2)}`);

        // 保存完整的调试数据
        await this.saveDebugInfo('complete_captcha_debug.json', debugData);

        // 修改判断逻辑：如果处理成功，优先相信处理结果
        if (success) {
            // 如果页面上没有验证码元素了，说明可能已经成功跳转
            if (finalStatus.visibleElements.length === 0 && finalStatus.hiddenInputs.length === 0) {
                this.log('✅ 验证码处理成功 (页面已无验证码元素)');
                await this.takeScreenshot('captcha_solved_no_elements');
                return true;
            }
            // 如果还有验证码元素，检查是否已解决
            else if (finalStatus.isResolved) {
                this.log('✅ 验证码处理成功 (验证码已解决)');
                await this.takeScreenshot('captcha_solved');
                return true;
            }
        }

        this.log('❌ 验证码处理失败');
        await this.takeScreenshot('captcha_failed', true);
        return false;
    }

    async checkCaptchaStatus() {
        try {
            const status = await this.page.evaluate(() => {
                const result = {
                    isResolved: false,
                    turnstileStatus: null,
                    recaptchaStatus: null,
                    visibleElements: [],
                    hiddenInputs: []
                };

                // 检查所有iframe和验证码相关元素
                const allIframes = document.querySelectorAll('iframe');
                const turnstileIframes = document.querySelectorAll('iframe[src*="challenges.cloudflare.com"]');
                const turnstileWidgets = document.querySelectorAll('[data-sitekey]');
                const cfTurnstile = document.querySelectorAll('.cf-turnstile');

                // 记录所有iframe
                allIframes.forEach((iframe, index) => {
                    const rect = iframe.getBoundingClientRect();
                    result.visibleElements.push({
                        type: 'iframe',
                        index,
                        visible: rect.width > 0 && rect.height > 0,
                        src: iframe.src || 'no-src',
                        dimensions: { width: rect.width, height: rect.height }
                    });
                });

                // 记录Turnstile相关元素
                turnstileWidgets.forEach((widget, index) => {
                    const rect = widget.getBoundingClientRect();
                    result.visibleElements.push({
                        type: 'turnstile-widget',
                        index,
                        visible: rect.width > 0 && rect.height > 0,
                        sitekey: widget.getAttribute('data-sitekey'),
                        dimensions: { width: rect.width, height: rect.height }
                    });
                });

                cfTurnstile.forEach((element, index) => {
                    const rect = element.getBoundingClientRect();
                    result.visibleElements.push({
                        type: 'cf-turnstile-element',
                        index,
                        visible: rect.width > 0 && rect.height > 0,
                        dimensions: { width: rect.width, height: rect.height }
                    });
                });

                // 检查Turnstile响应字段
                const turnstileInputs = document.querySelectorAll('input[name="cf-turnstile-response"]');
                turnstileInputs.forEach((input, index) => {
                    result.hiddenInputs.push({
                        type: 'turnstile-response',
                        index,
                        hasValue: input.value && input.value.length > 0,
                        valueLength: input.value ? input.value.length : 0
                    });

                    if (input.value && input.value.length > 0) {
                        result.isResolved = true;
                        result.turnstileStatus = 'resolved';
                    }
                });

                // 检查reCAPTCHA状态
                const recaptchaInputs = document.querySelectorAll('input[name="g-recaptcha-response"]');
                recaptchaInputs.forEach((input, index) => {
                    result.hiddenInputs.push({
                        type: 'recaptcha-response',
                        index,
                        hasValue: input.value && input.value.length > 0,
                        valueLength: input.value ? input.value.length : 0
                    });

                    if (input.value && input.value.length > 0) {
                        result.isResolved = true;
                        result.recaptchaStatus = 'resolved';
                    }
                });

                // 检查Cloudflare clearance cookie (更可靠的验证码通过指示)
                const cookies = document.cookie;
                if (cookies.includes('cf_clearance=')) {
                    result.isResolved = true;
                    result.turnstileStatus = 'cleared_by_cookie';
                }

                // 检查是否还有可见的验证码挑战
                const visibleChallenges = result.visibleElements.filter(el => el.visible);
                if (visibleChallenges.length > 0) {
                    // 如果还有可见的验证码挑战，则认为未解决
                    result.isResolved = false;
                }

                return result;
            });

            return status;
        } catch (error) {
            this.log(`检查验证码状态失败: ${error.message}`);
            return { isResolved: false, error: error.message };
        }
    }

    async tryClickCaptcha() {
        try {
            const clicked = await this.page.evaluate(() => {
                const clickableElements = [];

                // 收集所有可能的验证码元素
                const selectors = [
                    'iframe',
                    '[data-sitekey]',
                    '.cf-turnstile',
                    '[id*="turnstile"]',
                    '[class*="turnstile"]',
                    '.g-recaptcha',
                    '[id*="recaptcha"]',
                    '[class*="captcha"]',
                    '[id*="captcha"]'
                ];

                selectors.forEach(selector => {
                    const elements = document.querySelectorAll(selector);
                    elements.forEach(el => {
                        const rect = el.getBoundingClientRect();
                        if (rect.width > 0 && rect.height > 0) {
                            clickableElements.push({
                                element: el,
                                selector: selector,
                                rect: rect,
                                src: el.src || 'no-src',
                                id: el.id || 'no-id',
                                className: el.className || 'no-class'
                            });
                        }
                    });
                });

                console.log('Found clickable elements:', clickableElements.length);
                clickableElements.forEach((item, index) => {
                    console.log(`Element ${index}:`, {
                        selector: item.selector,
                        id: item.id,
                        className: item.className,
                        src: item.src,
                        dimensions: `${item.rect.width}x${item.rect.height}`
                    });
                });

                // 尝试点击第一个可见元素
                if (clickableElements.length > 0) {
                    try {
                        clickableElements[0].element.click();
                        console.log('Clicked element:', clickableElements[0].selector);
                        return true;
                    } catch (e) {
                        console.log('Click failed:', e.message);
                    }
                }

                return false;
            });

            return clicked;
        } catch (error) {
            this.log(`点击验证码失败: ${error.message}`);
            return false;
        }
    }

    async initBrowser() {
        try {
            // 获取代理配置
            this.currentProxy = await this.proxyHandler.getValidProxy();
            if (this.currentProxy) {
                this.log(`✅ 代理配置成功: ${this.currentProxy.host}:${this.currentProxy.port} (${this.currentProxy.protocol || 'http'})`);
                if (this.currentProxy.username) {
                    this.log(`🔐 代理认证: ${this.currentProxy.username}`);
                }
            } else {
                this.log('⚠️ 未配置代理，将直接连接');
            }
        } catch (error) {
            this.log(`❌ 代理配置失败: ${error.message}`);
            this.currentProxy = null;
        }

        // 使用增强的抗指纹检测配置
        const launchOptions = AntiFingerprint.getEnhancedLaunchOptions();

        // 添加代理配置到启动参数
        if (this.currentProxy) {
            const proxyArgs = this.proxyHandler.getPuppeteerProxyArgs();
            launchOptions.args.push(...proxyArgs);
            this.log(`🌐 浏览器代理参数: ${proxyArgs.join(' ')}`);
        }

        this.browser = await puppeteerExtra.launch(launchOptions);
        this.page = await this.browser.newPage();
        this.page.setDefaultTimeout(120000);
        this.page.setDefaultNavigationTimeout(120000);

        // 如果有代理认证，设置页面认证
        if (this.currentProxy && this.currentProxy.username && this.currentProxy.password) {
            await this.page.authenticate({
                username: this.currentProxy.username,
                password: this.currentProxy.password
            });
            this.log('🔐 页面代理认证已设置');
        }

        // 设置网络响应监听
        this.networkResponses = [];
        await this.page.setRequestInterception(true);

        this.page.on('request', (request) => {
            request.continue();
        });

        this.page.on('response', async (response) => {
            try {
                const url = response.url();
                const status = response.status();
                const headers = response.headers();

                // 只记录可能包含认证信息的响应
                if (url.includes('auth') || url.includes('login') || url.includes('token') ||
                    url.includes('api') || status === 200) {

                    let responseBody = null;
                    try {
                        const contentType = headers['content-type'] || '';
                        if (contentType.includes('application/json') || contentType.includes('text/')) {
                            responseBody = await response.text();
                        }
                    } catch (e) {
                        // 忽略无法读取的响应体
                    }

                    this.networkResponses.push({
                        url,
                        status,
                        headers,
                        body: responseBody,
                        timestamp: new Date().toISOString()
                    });
                }
            } catch (error) {
                // 忽略响应处理错误
            }
        });

        // 应用增强的抗指纹检测
        await AntiFingerprint.applyAntiFingerprinting(this.page);

        this.log('浏览器启动 (已启用增强抗指纹检测)');
        return this.page;
    }

    async findInputField(selectors) {
        for (const selector of selectors) {
            try {
                await this.page.waitForSelector(selector, { timeout: 5000 });
                const input = await this.page.$(selector);
                if (input) return input;
            } catch (e) { continue; }
        }
        return null;
    }

    async clickButtonContaining(keywords, timeout = 15000) {
        try {
            await this.page.waitForSelector('button, a, input[type="submit"], [role="button"]', { timeout });
            const elements = await this.page.$$('button, a, input[type="submit"], [role="button"]');

            for (const element of elements) {
                const text = await this.page.evaluate(el => {
                    return (el.textContent || el.value || el.getAttribute('aria-label') || '').trim();
                }, element);

                for (const keyword of keywords) {
                    if (text.toLowerCase().includes(keyword.toLowerCase())) {
                        await element.click();
                        await this.wait(1000);
                        return true;
                    }
                }
            }
            return false;
        } catch (error) {
            return false;
        }
    }

    async register() {
        try {
            await this.initBrowser();
            await this.takeScreenshot('browser_started');

            //
            this.authToken = await this.tokenHandler.getAuthToken(this.page, this.baseUrl);
            await this.takeScreenshot('auth_token_obtained');

            // 生成临时邮箱
            this.tempEmail = await this.emailHandler.generateTempEmail();
            this.log(`邮箱: ${this.tempEmail}`);

            const authUrl = await this.tokenHandler.getAuthUrl(this.baseUrl, this.authToken);
            this.log(`访问页面B: ${authUrl}`);
            await this.page.goto(authUrl, { waitUntil: 'domcontentloaded', timeout: 120000 });
            await this.wait(5000);
            await this.takeScreenshot('page_B_loaded');

            await this.handleCaptchaIfPresent();

            this.log('填入邮箱');
            const emailSelectors = ['input[type="email"]', 'input[name="email"]', 'input[name="username"]', 'input[id="username"]'];
            const emailInput = await this.findInputField(emailSelectors);
            if (!emailInput) {
                await this.takeScreenshot('no_email_input_B', true);
                throw new Error('页面B未找到邮箱输入框');
            }

            await emailInput.click();
            await this.wait(AntiFingerprint.humanDelay());
            await emailInput.type(this.tempEmail, { delay: AntiFingerprint.randomBetween(80, 150) });
            await this.takeScreenshot('email_entered_B');

            this.log('提交邮箱');
            const emailSubmitted = await this.clickButtonContaining(['Continue', 'Next', '继续']);
            if (!emailSubmitted) await this.page.keyboard.press('Enter');

            await this.wait(8000);
            await this.takeScreenshot('page_C_loaded');
            this.log(`页面C: ${this.page.url()}`);

            await this.handleCaptchaIfPresent();

            // 等待验证码 - email.js内部会自动处理邮件列表检查和验证码提取
            this.log('等待验证码...');
            const verificationCode = await this.emailHandler.waitForVerificationCode();

            this.log('填入验证码');
            const codeSelectors = ['input[name="code"]', 'input[placeholder*="code" i]', 'input[type="text"]:not([readonly])', 'input[autocomplete="one-time-code"]'];
            const codeInput = await this.findInputField(codeSelectors);
            if (!codeInput) {
                await this.takeScreenshot('no_code_input_C', true);
                throw new Error('页面C未找到验证码输入框');
            }

            await codeInput.click();
            await this.wait(AntiFingerprint.humanDelay());
            await codeInput.type(verificationCode, { delay: AntiFingerprint.randomBetween(80, 150) });
            await this.takeScreenshot('code_entered_C');

            await this.handleCaptchaIfPresent();

            this.log('提交验证码');
            const codeSubmitted = await this.clickButtonContaining(['Continue', 'Next', '继续', 'Verify']);
            if (!codeSubmitted) await this.page.keyboard.press('Enter');
            await this.wait(10000);
            await this.takeScreenshot('code_submitted_C');

            const pageContent = await this.page.content();
            if (pageContent.includes('error') || pageContent.includes('failed') || pageContent.includes('blocked')) {
                await this.takeScreenshot('registration_blocked', true);
                await this.handleCaptchaIfPresent();
                await this.takeScreenshot('retry_captcha');
            }

            try {
                const checkboxes = await this.page.$$('input[type="checkbox"], [role="checkbox"]');
                for (const checkbox of checkboxes) {
                    const isChecked = await this.page.evaluate(el =>
                        el.checked || el.getAttribute('aria-checked') === 'true', checkbox);
                    if (!isChecked) {
                        await checkbox.click();
                        await this.takeScreenshot('checkbox_checked');
                        break;
                    }
                }
            } catch (e) { }

            this.log('点击注册');
            const signupClicked = await this.clickButtonContaining(['sign up', 'register', '注册', 'create account']);
            if (!signupClicked) {
                await this.takeScreenshot('no_signup_button', true);
                throw new Error('未找到注册按钮');
            }
            await this.wait(15000);
            await this.takeScreenshot('signup_clicked');

            const jsonData = await this.getJsonData();
            if (jsonData) {
                // await this.tokenHandler.submitCallback(this.baseUrl, this.authToken, jsonData);
                console.log('成功获取到注册数据:');
                console.log(JSON.stringify(jsonData, null, 2));
                await this.takeScreenshot('data_logged');
            }

            await this.cleanup();

            const result = {
                email: this.tempEmail,
                proxy: this.currentProxy,
                status: 'success',
                timestamp: new Date().toISOString()
            };

            this.log(`注册完成: ${this.tempEmail}`);
            return result;

        } catch (error) {
            await this.takeScreenshot('registration_error', true);
            this.log(`注册失败: ${error.message}`);
            await this.cleanup();
            throw error;
        }
    }

    async getJsonData() {
        try {
            await this.page.evaluate(() => {
                window.clipboardData = '';
                if (navigator.clipboard && navigator.clipboard.writeText) {
                    const originalWriteText = navigator.clipboard.writeText;
                    navigator.clipboard.writeText = function (text) {
                        window.clipboardData = text;
                        return originalWriteText.call(this, text);
                    };
                }
            });

            const copyClicked = await this.clickButtonContaining(['copy', 'cop', '复制']);
            if (copyClicked) {
                await this.wait(2000);
                const clipboardData = await this.page.evaluate(() => window.clipboardData);
                if (clipboardData) {
                    return JSON.parse(clipboardData);
                }
            }

            const jsonData = await this.page.evaluate(() => {
                const scripts = Array.from(document.getElementsByTagName('script'));
                for (const script of scripts) {
                    if (script.textContent.includes('let data = {')) {
                        const match = script.textContent.match(/let data = ({[\s\S]*?});/);
                        if (match && match[1]) {
                            return JSON.parse(match[1]);
                        }
                    }
                }
                return null;
            });

            return jsonData;
        } catch (error) {
            return null;
        }
    }

    async cleanup() {
        if (this.emailHandler) this.emailHandler.clearCurrentEmail();
        if (this.oneMailHandler) this.oneMailHandler.clearCurrentEmail();
        if (this.proxyHandler) this.proxyHandler.clearProxy();
        if (this.browser) {
            try {
                await this.browser.close();
            } catch (error) { }
            this.browser = null;
            this.page = null;
        }
    }

    generateRandomEmail() {
        const randomString = Math.random().toString(36).substring(2, 10);
        return `abc+${randomString}@techexpresser.com`;
    }

    async captureNetworkResponses() {
        try {
            this.log('Capturing network responses and tokens...');

            // 等待一下确保所有请求完成
            await this.wait(2000);

            // 尝试从页面中获取所有可能的token和response数据
            const pageData = await this.page.evaluate(() => {
                const result = {
                    url: window.location.href,
                    localStorage: {},
                    sessionStorage: {},
                    cookies: document.cookie,
                    pageContent: document.documentElement.outerHTML,
                    tokens: []
                };

                // 获取localStorage
                try {
                    for (let i = 0; i < localStorage.length; i++) {
                        const key = localStorage.key(i);
                        result.localStorage[key] = localStorage.getItem(key);
                    }
                } catch (e) { }

                // 获取sessionStorage
                try {
                    for (let i = 0; i < sessionStorage.length; i++) {
                        const key = sessionStorage.key(i);
                        result.sessionStorage[key] = sessionStorage.getItem(key);
                    }
                } catch (e) { }

                // 查找页面中的token
                const tokenPatterns = [
                    /token["\s]*[:=]["\s]*([a-zA-Z0-9._-]+)/gi,
                    /access_token["\s]*[:=]["\s]*([a-zA-Z0-9._-]+)/gi,
                    /auth_token["\s]*[:=]["\s]*([a-zA-Z0-9._-]+)/gi,
                    /bearer["\s]*[:=]["\s]*([a-zA-Z0-9._-]+)/gi,
                    /jwt["\s]*[:=]["\s]*([a-zA-Z0-9._-]+)/gi
                ];

                tokenPatterns.forEach(pattern => {
                    let match;
                    while ((match = pattern.exec(result.pageContent)) !== null) {
                        result.tokens.push({
                            type: pattern.source.split('[')[0],
                            value: match[1]
                        });
                    }
                });

                return result;
            });

            // 保存完整的response数据到文件，控制台显示简要信息
            this.logger.saveDebugInfo('complete_response_data.json', pageData);
            this.logger.log('📊 完整响应数据已保存到调试文件');
            console.log('\n--- Session Storage ---');
            console.log(JSON.stringify(pageData.sessionStorage, null, 2));
            console.log('\n--- Found Tokens ---');
            console.log(JSON.stringify(pageData.tokens, null, 2));

            // 尝试获取网络请求的响应
            const responses = await this.page.evaluate(() => {
                // 如果页面有全局的响应数据，尝试获取
                const globalVars = [];
                for (const key in window) {
                    if (typeof window[key] === 'object' && window[key] !== null) {
                        try {
                            const str = JSON.stringify(window[key]);
                            if (str.includes('token') || str.includes('auth') || str.includes('access')) {
                                globalVars.push({
                                    key: key,
                                    value: window[key]
                                });
                            }
                        } catch (e) { }
                    }
                }
                return globalVars;
            });

            if (responses.length > 0) {
                console.log('\n--- Global Variables with Auth Data ---');
                console.log(JSON.stringify(responses, null, 2));
            }

            // 保存网络响应数据到文件
            if (this.networkResponses && this.networkResponses.length > 0) {
                this.logger.saveDebugInfo('network_responses.json', this.networkResponses);
                this.logger.log(`📡 ${this.networkResponses.length} 个网络响应已保存到调试文件`);
            }

        } catch (error) {
            this.log(`Error capturing responses: ${error.message}`);
        }
    }

    async signInAndSolveCaptcha(url) {
        try {
            await this.initBrowser();
            await this.takeScreenshot('browser_started');

            this.log(`Navigating to ${url}`);
            await this.page.goto(url, { waitUntil: 'networkidle2', timeout: 120000 });
            await this.takeScreenshot('page_loaded');

            this.log('Looking for "Sign In" button');
            const signInClicked = await this.clickButtonContaining(['sign in']);
            if (signInClicked) {
                this.log('Clicked "Sign In" button.');
                await this.wait(5000); // Wait for page transition
                await this.takeScreenshot('sign_in_clicked');
            } else {
                this.log('Could not find "Sign In" button.');
                await this.takeScreenshot('no_sign_in_button', true);
            }

            this.log('Checking for CAPTCHA...');
            const captchaSolved = await this.handleCaptchaIfPresent();

            if (captchaSolved) {
                this.log('CAPTCHA handled successfully.');

                // 步骤1: 截图
                await this.takeScreenshot('after_captcha_solved');

                // 步骤2: 输入邮箱地址
                const randomEmail = this.generateRandomEmail();
                this.log(`Generated email: ${randomEmail}`);
                console.log(`\n🔥 GENERATED EMAIL: ${randomEmail} 🔥\n`);

                const emailSelectors = [
                    'input[type="email"]',
                    'input[name="email"]',
                    'input[name="username"]',
                    'input[id="username"]',
                    'input[placeholder*="email" i]',
                    'input[placeholder*="Email" i]'
                ];

                const emailInput = await this.findInputField(emailSelectors);
                if (emailInput) {
                    await emailInput.click();
                    await this.wait(AntiFingerprint.humanDelay());
                    await emailInput.type(randomEmail, { delay: AntiFingerprint.randomBetween(80, 150) });
                    this.log('Email address entered successfully.');

                    // 步骤3: 截图
                    await this.takeScreenshot('email_entered');

                    // 步骤4: 点击continue按钮
                    this.log('Looking for Continue button...');
                    const continueClicked = await this.clickButtonContaining(['Continue', 'Next', '继续', 'Submit']);
                    if (continueClicked) {
                        this.log('Continue button clicked successfully.');
                        await this.wait(3000); // Wait for response

                        // 步骤5: 截图
                        await this.takeScreenshot('continue_clicked');

                        // 步骤6: 获取并输出完整的response
                        await this.captureNetworkResponses();

                    } else {
                        this.log('Could not find Continue button, trying Enter key...');
                        await this.page.keyboard.press('Enter');
                        await this.wait(3000);
                        await this.takeScreenshot('enter_pressed');
                        await this.captureNetworkResponses();
                    }
                } else {
                    this.log('Could not find email input field.');
                    await this.takeScreenshot('no_email_input', true);
                }

            } else {
                this.log('No CAPTCHA found or failed to handle.');
            }

            this.log('Process finished.');

        } catch (error) {
            await this.takeScreenshot('process_error', true);
            this.log(`An error occurred: ${error.message}`);
            throw error;
        } finally {
            await this.cleanup();
        }
    }

    // 模拟自然的打字输入
    async simulateNaturalTyping(inputElement, text) {
        try {
            // 清空输入框
            await inputElement.click({ clickCount: 3 });
            await this.wait(AntiFingerprint.randomBetween(100, 300));

            // 逐字符输入，模拟真人的不规律节奏
            for (let i = 0; i < text.length; i++) {
                const char = text[i];

                // 模拟偶尔的停顿（思考或查看验证码）
                if (i > 0 && Math.random() < 0.2) {
                    await this.wait(AntiFingerprint.randomBetween(300, 800));
                }

                // 输入字符，每个字符有不同的延迟
                await inputElement.type(char, {
                    delay: AntiFingerprint.randomBetween(120, 250)
                });

                // 模拟偶尔的回删重输（真人常见行为）
                if (i > 0 && Math.random() < 0.05) {
                    await this.wait(AntiFingerprint.randomBetween(200, 500));
                    await inputElement.press('Backspace');
                    await this.wait(AntiFingerprint.randomBetween(100, 300));
                    await inputElement.type(char, {
                        delay: AntiFingerprint.randomBetween(100, 200)
                    });
                }
            }

            // 输入完成后的短暂停顿
            await this.wait(AntiFingerprint.randomBetween(200, 600));

        } catch (error) {
            this.log(`自然输入模拟失败: ${error.message}`);
            // 回退到普通输入
            await inputElement.type(text, { delay: AntiFingerprint.randomBetween(80, 150) });
        }
    }

    // 增强的reCAPTCHA评分预热 - 模拟真人行为提升评分
    async performRecaptchaWarmup() {
        try {
            this.log('🎯 执行增强的reCAPTCHA评分预热行为...');

            // 1. 模拟真人进入页面的初始行为
            this.log('👁️ 模拟初始页面观察...');
            await this.simulateInitialPageObservation();

            // 2. 模拟阅读和理解页面内容
            this.log('📖 模拟阅读页面内容...');
            await this.simulateContentReading();

            // 3. 模拟自然的滚动行为
            this.log('📜 模拟自然滚动行为...');
            await this.simulateNaturalScrolling();

            // 4. 模拟鼠标轨迹和交互
            this.log('🖱️ 模拟真人鼠标行为...');
            await this.simulateHumanMouseBehavior();

            // 5. 模拟键盘交互和焦点管理
            this.log('⌨️ 模拟键盘和焦点交互...');
            await this.simulateKeyboardInteraction();

            // 6. 模拟页面元素交互
            this.log('🎯 模拟页面元素交互...');
            await this.simulateElementInteraction();

            // 7. 模拟决策思考过程
            this.log('🤔 模拟用户决策思考...');
            await this.simulateDecisionMaking();

            this.log('✅ 增强的reCAPTCHA评分预热完成');

        } catch (error) {
            this.log(`⚠️ reCAPTCHA预热过程中出错: ${error.message}`);
            // 预热失败不影响主流程，继续执行
        }
    }

    // 模拟初始页面观察
    async simulateInitialPageObservation() {
        // 模拟用户刚进入页面时的短暂停顿
        await this.wait(AntiFingerprint.randomBetween(800, 1500));

        // 模拟快速扫视页面
        const viewport = await this.page.viewport();
        const scanPoints = [
            { x: viewport.width * 0.1, y: viewport.height * 0.1 },
            { x: viewport.width * 0.9, y: viewport.height * 0.1 },
            { x: viewport.width * 0.5, y: viewport.height * 0.5 },
            { x: viewport.width * 0.1, y: viewport.height * 0.9 },
            { x: viewport.width * 0.9, y: viewport.height * 0.9 }
        ];

        for (const point of scanPoints) {
            await this.page.mouse.move(point.x, point.y, {
                steps: AntiFingerprint.randomBetween(3, 8)
            });
            await this.wait(AntiFingerprint.randomBetween(200, 500));
        }
    }

    // 模拟内容阅读
    async simulateContentReading() {
        await this.page.evaluate(() => {
            // 模拟阅读行为：查找文本内容并"停留"
            const textElements = document.querySelectorAll('h1, h2, h3, p, span, div');
            const readableElements = Array.from(textElements).filter(el => {
                const text = el.textContent.trim();
                return text.length > 10 && text.length < 200;
            });

            if (readableElements.length > 0) {
                // 随机选择几个元素进行"阅读"
                const elementsToRead = readableElements
                    .sort(() => Math.random() - 0.5)
                    .slice(0, Math.min(3, readableElements.length));

                elementsToRead.forEach((element, index) => {
                    setTimeout(() => {
                        element.scrollIntoView({ behavior: 'smooth', block: 'center' });
                        // 触发视觉焦点事件
                        element.dispatchEvent(new Event('focus', { bubbles: true }));
                    }, index * 1000);
                });
            }
        });

        await this.wait(AntiFingerprint.randomBetween(3000, 6000));
    }

    // 模拟自然滚动
    async simulateNaturalScrolling() {
        const scrollBehaviors = [
            'smooth_down', 'quick_scan', 'careful_read', 'back_and_forth'
        ];
        const behavior = scrollBehaviors[Math.floor(Math.random() * scrollBehaviors.length)];

        switch (behavior) {
            case 'smooth_down':
                await this.smoothScrollDown();
                break;
            case 'quick_scan':
                await this.quickScanScroll();
                break;
            case 'careful_read':
                await this.carefulReadScroll();
                break;
            case 'back_and_forth':
                await this.backAndForthScroll();
                break;
        }
    }

    // 平滑向下滚动
    async smoothScrollDown() {
        const scrollHeight = await this.page.evaluate(() => document.body.scrollHeight);
        const viewportHeight = await this.page.evaluate(() => window.innerHeight);
        const totalScrollDistance = scrollHeight - viewportHeight;

        if (totalScrollDistance > 0) {
            const scrollSteps = AntiFingerprint.randomBetween(5, 10);
            const stepSize = totalScrollDistance / scrollSteps;

            for (let i = 0; i < scrollSteps; i++) {
                await this.page.evaluate((scrollY) => {
                    window.scrollTo({ top: scrollY, behavior: 'smooth' });
                }, stepSize * (i + 1));

                await this.wait(AntiFingerprint.randomBetween(800, 1500));

                // 偶尔停顿更久（模拟阅读）
                if (Math.random() < 0.3) {
                    await this.wait(AntiFingerprint.randomBetween(2000, 4000));
                }
            }
        }
    }

    // 快速扫描滚动
    async quickScanScroll() {
        const scrollHeight = await this.page.evaluate(() => document.body.scrollHeight);
        const viewportHeight = await this.page.evaluate(() => window.innerHeight);

        // 快速滚动到底部
        await this.page.evaluate(() => {
            window.scrollTo({ top: document.body.scrollHeight, behavior: 'smooth' });
        });
        await this.wait(AntiFingerprint.randomBetween(1000, 2000));

        // 快速滚动回顶部
        await this.page.evaluate(() => {
            window.scrollTo({ top: 0, behavior: 'smooth' });
        });
        await this.wait(AntiFingerprint.randomBetween(1000, 2000));
    }

    // 仔细阅读滚动
    async carefulReadScroll() {
        const elements = await this.page.$$('h1, h2, h3, p');

        for (let i = 0; i < Math.min(elements.length, 5); i++) {
            const element = elements[i];
            await element.scrollIntoView();
            await this.wait(AntiFingerprint.randomBetween(2000, 4000));

            // 模拟阅读时的微小滚动调整
            await this.page.evaluate(() => {
                window.scrollBy(0, Math.random() * 20 - 10);
            });
            await this.wait(AntiFingerprint.randomBetween(500, 1000));
        }
    }

    // 来回滚动
    async backAndForthScroll() {
        const positions = [0, 0.3, 0.6, 0.4, 0.8, 0.2, 1.0];

        for (const position of positions) {
            await this.page.evaluate((pos) => {
                const scrollY = document.body.scrollHeight * pos;
                window.scrollTo({ top: scrollY, behavior: 'smooth' });
            }, position);

            await this.wait(AntiFingerprint.randomBetween(1000, 2500));
        }
    }

    // 模拟真人鼠标行为
    async simulateHumanMouseBehavior() {
        const viewport = await this.page.viewport();
        const mousePatterns = ['random_walk', 'circular', 'zigzag', 'hover_elements'];
        const pattern = mousePatterns[Math.floor(Math.random() * mousePatterns.length)];

        switch (pattern) {
            case 'random_walk':
                await this.randomMouseWalk(viewport);
                break;
            case 'circular':
                await this.circularMouseMovement(viewport);
                break;
            case 'zigzag':
                await this.zigzagMouseMovement(viewport);
                break;
            case 'hover_elements':
                await this.hoverOverElements();
                break;
        }
    }

    // 随机鼠标游走
    async randomMouseWalk(viewport) {
        let currentX = viewport.width / 2;
        let currentY = viewport.height / 2;

        for (let i = 0; i < AntiFingerprint.randomBetween(5, 10); i++) {
            const deltaX = AntiFingerprint.randomBetween(-200, 200);
            const deltaY = AntiFingerprint.randomBetween(-200, 200);

            currentX = Math.max(50, Math.min(viewport.width - 50, currentX + deltaX));
            currentY = Math.max(50, Math.min(viewport.height - 50, currentY + deltaY));

            await this.page.mouse.move(currentX, currentY, {
                steps: AntiFingerprint.randomBetween(10, 25)
            });
            await this.wait(AntiFingerprint.randomBetween(300, 800));

            // 偶尔停顿
            if (Math.random() < 0.3) {
                await this.wait(AntiFingerprint.randomBetween(1000, 2000));
            }
        }
    }

    // 圆形鼠标移动
    async circularMouseMovement(viewport) {
        const centerX = viewport.width / 2;
        const centerY = viewport.height / 2;
        const radius = Math.min(viewport.width, viewport.height) / 4;

        for (let angle = 0; angle < Math.PI * 2; angle += Math.PI / 8) {
            const x = centerX + Math.cos(angle) * radius;
            const y = centerY + Math.sin(angle) * radius;

            await this.page.mouse.move(x, y, { steps: AntiFingerprint.randomBetween(5, 15) });
            await this.wait(AntiFingerprint.randomBetween(200, 500));
        }
    }

    // 之字形鼠标移动
    async zigzagMouseMovement(viewport) {
        const steps = 6;
        const stepWidth = viewport.width / steps;

        for (let i = 0; i < steps; i++) {
            const x = stepWidth * i + AntiFingerprint.randomBetween(-50, 50);
            const y = (i % 2 === 0) ? viewport.height * 0.3 : viewport.height * 0.7;

            await this.page.mouse.move(x, y, { steps: AntiFingerprint.randomBetween(8, 20) });
            await this.wait(AntiFingerprint.randomBetween(400, 900));
        }
    }

    // 悬停在元素上
    async hoverOverElements() {
        const elements = await this.page.$$('button, input, a, h1, h2, h3');
        const elementsToHover = elements.slice(0, Math.min(5, elements.length));

        for (const element of elementsToHover) {
            try {
                const box = await element.boundingBox();
                if (box) {
                    const x = box.x + box.width / 2 + AntiFingerprint.randomBetween(-10, 10);
                    const y = box.y + box.height / 2 + AntiFingerprint.randomBetween(-10, 10);

                    await this.page.mouse.move(x, y, { steps: AntiFingerprint.randomBetween(10, 20) });
                    await this.wait(AntiFingerprint.randomBetween(800, 1500));

                    // 模拟悬停效果
                    await element.hover();
                    await this.wait(AntiFingerprint.randomBetween(500, 1200));
                }
            } catch (error) {
                // 忽略元素不可见等错误
            }
        }
    }

    // 模拟键盘交互
    async simulateKeyboardInteraction() {
        const interactions = ['tab_navigation', 'shortcut_keys', 'text_selection'];
        const interaction = interactions[Math.floor(Math.random() * interactions.length)];

        switch (interaction) {
            case 'tab_navigation':
                await this.simulateTabNavigation();
                break;
            case 'shortcut_keys':
                await this.simulateShortcutKeys();
                break;
            case 'text_selection':
                await this.simulateTextSelection();
                break;
        }
    }

    // Tab导航
    async simulateTabNavigation() {
        const tabCount = AntiFingerprint.randomBetween(3, 7);

        for (let i = 0; i < tabCount; i++) {
            await this.page.keyboard.press('Tab');
            await this.wait(AntiFingerprint.randomBetween(400, 1000));

            // 偶尔按Shift+Tab返回
            if (Math.random() < 0.2 && i > 0) {
                await this.page.keyboard.down('Shift');
                await this.page.keyboard.press('Tab');
                await this.page.keyboard.up('Shift');
                await this.wait(AntiFingerprint.randomBetween(300, 800));
            }
        }
    }

    // 快捷键操作
    async simulateShortcutKeys() {
        const shortcuts = [
            () => this.page.keyboard.press('F5'), // 刷新（但不实际执行）
            () => { // Ctrl+A (全选)
                this.page.keyboard.down('Control');
                this.page.keyboard.press('KeyA');
                this.page.keyboard.up('Control');
            },
            () => this.page.keyboard.press('Home'), // 回到顶部
            () => this.page.keyboard.press('End')   // 到底部
        ];

        const shortcut = shortcuts[Math.floor(Math.random() * shortcuts.length)];
        await shortcut();
        await this.wait(AntiFingerprint.randomBetween(500, 1200));
    }

    // 文本选择
    async simulateTextSelection() {
        await this.page.evaluate(() => {
            const textElements = document.querySelectorAll('p, span, div');
            const textElement = textElements[Math.floor(Math.random() * textElements.length)];

            if (textElement && textElement.textContent.length > 20) {
                const range = document.createRange();
                const text = textElement.textContent;
                const start = Math.floor(Math.random() * (text.length - 10));
                const end = start + Math.floor(Math.random() * 10) + 5;

                range.setStart(textElement.firstChild, start);
                range.setEnd(textElement.firstChild, Math.min(end, text.length));

                const selection = window.getSelection();
                selection.removeAllRanges();
                selection.addRange(range);

                // 短暂保持选择状态
                setTimeout(() => {
                    selection.removeAllRanges();
                }, 1000);
            }
        });

        await this.wait(AntiFingerprint.randomBetween(1000, 2000));
    }

    // 模拟元素交互
    async simulateElementInteraction() {
        await this.page.evaluate(() => {
            // 触发各种DOM事件来模拟真实交互
            const events = ['mouseenter', 'mouseleave', 'focus', 'blur', 'scroll'];
            const elements = document.querySelectorAll('button, input, a, div');

            for (let i = 0; i < Math.min(5, elements.length); i++) {
                const element = elements[Math.floor(Math.random() * elements.length)];
                const event = events[Math.floor(Math.random() * events.length)];

                setTimeout(() => {
                    element.dispatchEvent(new Event(event, { bubbles: true }));
                }, i * 200);
            }

            // 模拟窗口事件
            setTimeout(() => {
                window.dispatchEvent(new Event('resize'));
                document.dispatchEvent(new Event('visibilitychange'));
            }, 1000);
        });

        await this.wait(AntiFingerprint.randomBetween(2000, 3000));
    }

    // 模拟决策思考
    async simulateDecisionMaking() {
        // 模拟用户在做决定前的行为
        const thinkingBehaviors = [
            'scroll_review', 'element_inspection', 'pause_and_think'
        ];

        const behavior = thinkingBehaviors[Math.floor(Math.random() * thinkingBehaviors.length)];

        switch (behavior) {
            case 'scroll_review':
                // 快速回顾页面内容
                await this.page.evaluate(() => {
                    window.scrollTo({ top: 0, behavior: 'smooth' });
                });
                await this.wait(1000);
                await this.page.evaluate(() => {
                    window.scrollTo({ top: document.body.scrollHeight / 2, behavior: 'smooth' });
                });
                await this.wait(AntiFingerprint.randomBetween(2000, 4000));
                break;

            case 'element_inspection':
                // 检查重要元素
                const buttons = await this.page.$$('button, input[type="submit"]');
                if (buttons.length > 0) {
                    const button = buttons[0];
                    await button.hover();
                    await this.wait(AntiFingerprint.randomBetween(1500, 3000));
                }
                break;

            case 'pause_and_think':
                // 简单的思考停顿
                await this.wait(AntiFingerprint.randomBetween(3000, 6000));
                break;
        }
    }

    // 模拟等待邮件到达的真人行为
    async simulateWaitingForEmail() {
        try {
            this.log('⏳ 模拟等待邮件到达的行为...');

            // 1. 模拟切换到邮箱标签页的行为
            this.log('🔄 模拟切换到邮箱标签页...');
            await this.simulateTabSwitching();

            // 2. 模拟在邮箱中的行为
            this.log('📧 模拟在邮箱中查看邮件...');
            await this.simulateEmailChecking();

            // 3. 模拟等待过程中的不耐烦行为
            this.log('😤 模拟等待过程中的不耐烦行为...');
            await this.simulateImpatientWaiting();

            // 4. 模拟刷新邮箱
            this.log('🔄 模拟刷新邮箱查看新邮件...');
            await this.simulateEmailRefresh();

        } catch (error) {
            this.log(`⚠️ 模拟等待邮件行为出错: ${error.message}`);
        }
    }

    // 模拟标签页切换
    async simulateTabSwitching() {
        // 模拟Ctrl+T打开新标签页
        await this.page.keyboard.down('Control');
        await this.page.keyboard.press('KeyT');
        await this.page.keyboard.up('Control');
        await this.wait(AntiFingerprint.randomBetween(500, 1200));

        // 模拟在地址栏输入gmail.com（但不实际导航）
        await this.page.keyboard.press('KeyG');
        await this.wait(AntiFingerprint.randomBetween(100, 300));
        await this.page.keyboard.press('KeyM');
        await this.wait(AntiFingerprint.randomBetween(100, 300));
        await this.page.keyboard.press('KeyA');
        await this.wait(AntiFingerprint.randomBetween(100, 300));
        await this.page.keyboard.press('KeyI');
        await this.wait(AntiFingerprint.randomBetween(100, 300));
        await this.page.keyboard.press('KeyL');
        await this.wait(AntiFingerprint.randomBetween(200, 500));

        // 模拟按Enter（但不实际执行）
        // await this.page.keyboard.press('Enter');

        // 模拟切换回原标签页 Ctrl+Shift+Tab
        await this.wait(AntiFingerprint.randomBetween(1000, 2000));
        await this.page.keyboard.down('Control');
        await this.page.keyboard.down('Shift');
        await this.page.keyboard.press('Tab');
        await this.page.keyboard.up('Shift');
        await this.page.keyboard.up('Control');
        await this.wait(AntiFingerprint.randomBetween(300, 800));
    }

    // 模拟在邮箱中查看邮件
    async simulateEmailChecking() {
        // 模拟滚动查看邮件列表
        await this.page.evaluate(() => {
            // 模拟在邮箱中滚动
            let scrollPosition = 0;
            const scrollInterval = setInterval(() => {
                scrollPosition += 100;
                window.scrollTo(0, scrollPosition);
                if (scrollPosition > 300) {
                    clearInterval(scrollInterval);
                }
            }, 200);
        });

        await this.wait(AntiFingerprint.randomBetween(2000, 4000));

        // 模拟点击刷新按钮的鼠标移动
        const viewport = await this.page.viewport();
        const refreshButtonArea = {
            x: viewport.width * 0.1, // 假设刷新按钮在左上角
            y: viewport.height * 0.1
        };

        await this.page.mouse.move(
            refreshButtonArea.x + AntiFingerprint.randomBetween(-20, 20),
            refreshButtonArea.y + AntiFingerprint.randomBetween(-20, 20),
            { steps: AntiFingerprint.randomBetween(10, 20) }
        );
        await this.wait(AntiFingerprint.randomBetween(500, 1200));
    }

    // 模拟不耐烦等待
    async simulateImpatientWaiting() {
        const impatientBehaviors = [
            'tap_fingers', 'check_time', 'scroll_randomly', 'switch_tabs'
        ];

        const behavior = impatientBehaviors[Math.floor(Math.random() * impatientBehaviors.length)];

        switch (behavior) {
            case 'tap_fingers':
                // 模拟敲击键盘（但不输入）
                for (let i = 0; i < AntiFingerprint.randomBetween(3, 7); i++) {
                    await this.page.keyboard.press('Space');
                    await this.wait(AntiFingerprint.randomBetween(200, 500));
                }
                break;

            case 'check_time':
                // 模拟查看时间的鼠标移动
                const viewport = await this.page.viewport();
                await this.page.mouse.move(
                    viewport.width * 0.9, // 右下角时间区域
                    viewport.height * 0.95,
                    { steps: AntiFingerprint.randomBetween(5, 15) }
                );
                await this.wait(AntiFingerprint.randomBetween(1000, 2000));
                break;

            case 'scroll_randomly':
                // 随机滚动
                for (let i = 0; i < 3; i++) {
                    await this.page.evaluate(() => {
                        window.scrollBy(0, Math.random() * 200 - 100);
                    });
                    await this.wait(AntiFingerprint.randomBetween(300, 800));
                }
                break;

            case 'switch_tabs':
                // 模拟快速切换标签页
                await this.page.keyboard.down('Control');
                await this.page.keyboard.press('Tab');
                await this.page.keyboard.up('Control');
                await this.wait(AntiFingerprint.randomBetween(500, 1000));
                await this.page.keyboard.down('Control');
                await this.page.keyboard.down('Shift');
                await this.page.keyboard.press('Tab');
                await this.page.keyboard.up('Shift');
                await this.page.keyboard.up('Control');
                break;
        }

        await this.wait(AntiFingerprint.randomBetween(1000, 3000));
    }

    // 模拟刷新邮箱
    async simulateEmailRefresh() {
        // 模拟按F5刷新
        await this.page.keyboard.press('F5');
        await this.wait(AntiFingerprint.randomBetween(1000, 2000));

        // 或者模拟Ctrl+R刷新
        if (Math.random() < 0.5) {
            await this.page.keyboard.down('Control');
            await this.page.keyboard.press('KeyR');
            await this.page.keyboard.up('Control');
            await this.wait(AntiFingerprint.randomBetween(800, 1500));
        }

        // 模拟等待页面加载
        this.log('⏳ 模拟等待邮箱页面加载...');
        await this.wait(AntiFingerprint.randomBetween(2000, 4000));
    }

    // 模拟从邮箱复制验证码后的行为
    async simulateAfterCopyingCode() {
        try {
            this.log('📋 模拟从邮箱复制验证码的过程...');

            // 1. 模拟找到验证码邮件
            this.log('🔍 模拟找到并打开验证码邮件...');
            await this.simulateFindingVerificationEmail();

            // 2. 模拟选择和复制验证码
            this.log('📝 模拟选择和复制验证码...');
            await this.simulateCopyingCode();

            // 3. 模拟切换回原页面
            this.log('🔄 模拟切换回验证页面...');
            await this.simulateSwitchingBackToVerification();

        } catch (error) {
            this.log(`⚠️ 模拟复制验证码行为出错: ${error.message}`);
        }
    }

    // 模拟找到验证码邮件
    async simulateFindingVerificationEmail() {
        // 模拟在邮件列表中寻找
        const viewport = await this.page.viewport();

        // 模拟扫视邮件列表
        const emailPositions = [
            { x: viewport.width * 0.3, y: viewport.height * 0.3 },
            { x: viewport.width * 0.3, y: viewport.height * 0.4 },
            { x: viewport.width * 0.3, y: viewport.height * 0.5 },
            { x: viewport.width * 0.3, y: viewport.height * 0.6 }
        ];

        for (const pos of emailPositions) {
            await this.page.mouse.move(pos.x, pos.y, {
                steps: AntiFingerprint.randomBetween(5, 12)
            });
            await this.wait(AntiFingerprint.randomBetween(800, 1500));

            // 模拟找到目标邮件时的停顿
            if (Math.random() < 0.7) { // 70%概率找到
                this.log('👀 模拟找到验证码邮件...');
                await this.wait(AntiFingerprint.randomBetween(1000, 2000));

                // 模拟双击打开邮件
                await this.page.mouse.click(pos.x, pos.y, { clickCount: 2 });
                await this.wait(AntiFingerprint.randomBetween(1500, 3000));
                break;
            }
        }
    }

    // 模拟复制验证码
    async simulateCopyingCode() {
        // 模拟在邮件中寻找验证码
        this.log('🔍 模拟在邮件中寻找验证码...');

        // 模拟滚动查看邮件内容
        await this.page.evaluate(() => {
            window.scrollBy(0, 100);
        });
        await this.wait(AntiFingerprint.randomBetween(1000, 2000));

        // 模拟找到验证码并选择
        const viewport = await this.page.viewport();
        const codePosition = {
            x: viewport.width * 0.5,
            y: viewport.height * 0.6
        };

        // 模拟三击选择验证码
        await this.page.mouse.click(codePosition.x, codePosition.y, { clickCount: 3 });
        await this.wait(AntiFingerprint.randomBetween(500, 1000));

        // 模拟Ctrl+C复制
        await this.page.keyboard.down('Control');
        await this.page.keyboard.press('KeyC');
        await this.page.keyboard.up('Control');
        await this.wait(AntiFingerprint.randomBetween(300, 800));

        this.log('📋 模拟验证码已复制到剪贴板');
    }

    // 模拟切换回验证页面
    async simulateSwitchingBackToVerification() {
        // 模拟Alt+Tab切换窗口
        await this.page.keyboard.down('Alt');
        await this.page.keyboard.press('Tab');
        await this.page.keyboard.up('Alt');
        await this.wait(AntiFingerprint.randomBetween(500, 1200));

        // 或者模拟Ctrl+Tab切换标签页
        if (Math.random() < 0.5) {
            await this.page.keyboard.down('Control');
            await this.page.keyboard.press('Tab');
            await this.page.keyboard.up('Control');
            await this.wait(AntiFingerprint.randomBetween(300, 800));
        }

        // 模拟重新聚焦到验证页面
        this.log('🎯 模拟重新聚焦到验证页面...');
        await this.wait(AntiFingerprint.randomBetween(800, 1500));

        // 模拟点击页面确保焦点
        const viewport = await this.page.viewport();
        await this.page.mouse.click(
            viewport.width / 2 + AntiFingerprint.randomBetween(-100, 100),
            viewport.height / 2 + AntiFingerprint.randomBetween(-100, 100)
        );
        await this.wait(AntiFingerprint.randomBetween(300, 800));
    }

    // 模拟视觉搜索输入框
    async simulateVisualSearch(targetElement) {
        try {
            const box = await targetElement.boundingBox();
            if (box) {
                const viewport = await this.page.viewport();

                // 模拟眼睛扫视页面寻找输入框
                const searchPoints = [
                    { x: viewport.width * 0.3, y: viewport.height * 0.3 },
                    { x: viewport.width * 0.7, y: viewport.height * 0.3 },
                    { x: viewport.width * 0.5, y: viewport.height * 0.5 },
                    { x: box.x + box.width / 2, y: box.y + box.height / 2 } // 最终找到目标
                ];

                for (const point of searchPoints) {
                    await this.page.mouse.move(point.x, point.y, {
                        steps: AntiFingerprint.randomBetween(3, 8)
                    });
                    await this.wait(AntiFingerprint.randomBetween(300, 800));
                }

                // 找到目标后的短暂停顿
                await this.wait(AntiFingerprint.randomBetween(500, 1200));
            }
        } catch (error) {
            this.log(`视觉搜索模拟失败: ${error.message}`);
        }
    }

    // 模拟粘贴验证码
    async simulatePastingCode(inputElement, code) {
        try {
            // 确保输入框获得焦点
            await inputElement.focus();
            await this.wait(AntiFingerprint.randomBetween(200, 500));

            // 模拟Ctrl+A全选（清空可能存在的内容）
            await this.page.keyboard.down('Control');
            await this.page.keyboard.press('KeyA');
            await this.page.keyboard.up('Control');
            await this.wait(AntiFingerprint.randomBetween(100, 300));

            // 模拟Ctrl+V粘贴
            await this.page.keyboard.down('Control');
            await this.page.keyboard.press('KeyV');
            await this.page.keyboard.up('Control');
            await this.wait(AntiFingerprint.randomBetween(200, 500));

            // 实际输入验证码（因为我们没有真正的剪贴板内容）
            await inputElement.type(code, { delay: AntiFingerprint.randomBetween(50, 100) });

        } catch (error) {
            this.log(`粘贴验证码模拟失败: ${error.message}`);
            // 回退到普通输入
            await inputElement.type(code, { delay: AntiFingerprint.randomBetween(80, 150) });
        }
    }

    // 模拟粘贴后的验证行为
    async simulateAfterPastingVerification() {
        // 1. 模拟检查粘贴的内容是否正确
        this.log('👀 模拟检查粘贴的验证码...');
        await this.wait(AntiFingerprint.randomBetween(1000, 2000));

        // 2. 模拟选择验证码文本查看
        await this.page.keyboard.down('Control');
        await this.page.keyboard.press('KeyA');
        await this.page.keyboard.up('Control');
        await this.wait(AntiFingerprint.randomBetween(800, 1500));

        // 3. 模拟取消选择（点击其他地方）
        const viewport = await this.page.viewport();
        await this.page.mouse.click(
            viewport.width * 0.8,
            viewport.height * 0.3,
            { delay: AntiFingerprint.randomBetween(100, 300) }
        );
        await this.wait(AntiFingerprint.randomBetween(500, 1200));

        // 4. 模拟最终确认思考
        this.log('🤔 模拟最终确认思考...');
        await this.simulateHesitation();

        // 5. 模拟检查验证码是否正确输入
        this.log('👁️ 模拟检查输入内容...');
        await this.simulateRandomMouseMovement();
        await this.wait(AntiFingerprint.randomBetween(1000, 2500));

        // 6. 模拟再次确认验证码
        this.log('🤔 模拟再次确认验证码...');
        await this.simulateHesitation();
    }

    // 新方法：处理邮箱验证码流程
    async handleEmailVerificationWithOneMailAPI(url) {
        try {
            await this.initBrowser();

            // 设置网络监听以捕获授权相关的请求和响应
            await this.setupNetworkMonitoring();

            await this.takeScreenshot('browser_started');
            await this.saveHtmlContent('browser_started');

            // 测试One Mail API连接
            const apiConnected = await this.oneMailHandler.testConnection();
            if (!apiConnected) {
                throw new Error('One Mail API连接失败');
            }

            this.log(`导航到页面: ${url}`);
            await this.page.goto(url, { waitUntil: 'networkidle2', timeout: 120000 });
            await this.takeScreenshot('page_loaded');
            await this.saveHtmlContent('page_loaded');

            // 检查是否直接到了登录页面（通过URL判断）
            const currentUrl = this.page.url();
            if (currentUrl.includes('login') || currentUrl.includes('auth')) {
                this.log('✅ 已直接进入登录页面，跳过Sign In步骤');
            } else {
                // 如果不是登录页面，尝试点击 Sign In 按钮
                this.log('查找并点击 Sign In 按钮...');
                const signInClicked = await this.clickButtonContaining(['sign in', 'login', '登录', '登入']);
                if (signInClicked) {
                    this.log('✅ 点击了 Sign In 按钮');
                    await this.wait(5000); // 等待页面跳转
                    await this.takeScreenshot('sign_in_clicked');
                    await this.saveHtmlContent('sign_in_clicked');
                } else {
                    this.log('❌ 未找到 Sign In 按钮');
                    await this.takeScreenshot('no_sign_in_button', true);
                    await this.saveHtmlContent('no_sign_in_button', true);
                    throw new Error('未找到 Sign In 按钮');
                }
            }

            // 处理可能的验证码
            await this.handleCaptchaIfPresent();

            // 现在生成临时邮箱
            const tempEmail = await this.oneMailHandler.generateEmail();
            this.log(`生成的邮箱: ${tempEmail}`);

            // 查找邮箱输入框并填入邮箱
            const emailInputSelectors = [
                'input[type="email"]',
                'input[name="email"]',
                'input[name="username"]',
                'input[id="username"]',
                'input[inputmode="email"]',
                'input[placeholder*="email"]',
                'input[placeholder*="Email"]',
                'input[id*="email"]',
                'input[class*="email"]'
            ];

            const emailInput = await this.findInputField(emailInputSelectors);
            if (emailInput) {
                await emailInput.click();
                await this.wait(AntiFingerprint.humanDelay());
                await emailInput.type(tempEmail, { delay: AntiFingerprint.randomBetween(80, 150) });
                this.log(`邮箱已填入: ${tempEmail}`);

                // 邮箱输入后的真人行为
                await this.simulateHesitation();
                await this.simulateFocusChange();

                await this.takeScreenshot('email_entered');
                await this.saveHtmlContent('email_entered');

                // 点击Continue按钮
                const continueClicked = await this.clickButtonContaining(['continue', 'next', '继续', '下一步']);
                if (continueClicked) {
                    this.log('点击了Continue按钮');
                    await this.wait(5000);
                    await this.takeScreenshot('continue_clicked');
                    await this.saveHtmlContent('continue_clicked');
                } else {
                    this.log('未找到Continue按钮，尝试按Enter键');
                    await this.page.keyboard.press('Enter');
                    await this.wait(5000);
                    await this.takeScreenshot('enter_pressed');
                    await this.saveHtmlContent('enter_pressed');
                }

                // 等待验证码输入页面加载
                await this.wait(3000);
                await this.takeScreenshot('verification_page_loaded');
                await this.saveHtmlContent('verification_page_loaded');

                // 开始获取验证码 - 模拟真人查看邮箱的过程
                this.log('📧 开始获取验证码，模拟查看邮箱行为...');

                // 模拟等待邮件到达的行为
                await this.simulateWaitingForEmail();

                // 实际获取验证码（2分钟超时，每5秒检查一次）
                this.log('🔍 开始检查邮箱中的验证码...');
                const verificationCode = await this.oneMailHandler.getVerificationCode(tempEmail, 2);
                this.log(`📨 获取到验证码: ${verificationCode}`);

                // 模拟从邮箱复制验证码后的行为
                this.log('📋 模拟从邮箱复制验证码后的行为...');
                await this.simulateAfterCopyingCode();

                // 查找验证码输入框
                const codeInputSelectors = [
                    'input[type="text"]',
                    'input[name="code"]',
                    'input[name="verification_code"]',
                    'input[name="verificationCode"]',
                    'input[placeholder*="code"]',
                    'input[placeholder*="Code"]',
                    'input[placeholder*="验证码"]',
                    'input[id*="code"]',
                    'input[class*="code"]'
                ];

                const codeInput = await this.findInputField(codeInputSelectors);
                if (codeInput) {
                    // 模拟从邮箱切换回来后定位输入框
                    this.log('👁️ 切换回页面后，定位验证码输入框...');
                    await this.wait(AntiFingerprint.randomBetween(800, 1500));

                    // 模拟寻找输入框的视觉过程
                    await this.simulateVisualSearch(codeInput);

                    await codeInput.click();
                    await this.wait(AntiFingerprint.humanDelay());

                    // 模拟粘贴验证码（Ctrl+V）而不是打字
                    this.log('📋 模拟粘贴验证码 (Ctrl+V)...');
                    await this.simulatePastingCode(codeInput, verificationCode);
                    this.log(`验证码已粘贴: ${verificationCode}`);

                    // 验证码粘贴后的确认行为
                    this.log('🤔 粘贴完成，模拟检查确认...');
                    await this.simulateAfterPastingVerification();

                    // 模拟检查验证码是否正确输入
                    this.log('👁️ 模拟检查输入内容...');
                    await this.simulateRandomMouseMovement();
                    await this.wait(AntiFingerprint.randomBetween(800, 1500));

                    // 模拟再次确认
                    this.log('🔍 模拟再次确认验证码...');
                    await this.simulateHesitation();

                    await this.takeScreenshot('code_entered');
                    await this.saveHtmlContent('code_entered');

                    // 在点击Continue按钮前，进行reCAPTCHA评分预热
                    this.log('🔥 开始reCAPTCHA评分预热...');
                    await this.performRecaptchaWarmup();

                    // 再次点击Continue按钮
                    const finalContinueClicked = await this.clickButtonContaining(['continue', 'verify', 'submit', '继续', '验证', '提交']);
                    if (finalContinueClicked) {
                        this.log('点击了最终的Continue按钮');
                        await this.wait(5000);
                        await this.takeScreenshot('final_continue_clicked');
                        await this.saveHtmlContent('final_continue_clicked');

                        // STEP 11: 检查并处理自动提交页面的reCAPTCHA
                        this.log('🔍 STEP 11: 检查是否为自动提交验证页面...');

                        // 等待页面稳定，避免在导航过程中检查
                        await this.wait(2000);

                        let isAutoSubmitPage = false;
                        try {
                            isAutoSubmitPage = await this.page.evaluate(() => {
                                // 检查页面是否还存在
                                if (!document || !document.body) {
                                    return false;
                                }

                                const bodyContent = document.body.innerHTML || '';
                                return bodyContent.includes('Auto-submit logic') ||
                                       bodyContent.includes('onClick') ||
                                       document.querySelector('#action-form') ||
                                       bodyContent.includes('grecaptcha.enterprise.execute') ||
                                       bodyContent.includes('Verifying you are human');
                            });
                        } catch (error) {
                            this.log(`⚠️ STEP 11: 页面检查失败 (可能已导航): ${error.message}`);
                            // 如果页面已经导航，检查当前URL是否表明成功
                            const currentUrl = this.page.url();
                            this.log(`📍 当前页面URL: ${currentUrl}`);

                            if (currentUrl.includes('success') || currentUrl.includes('complete') || currentUrl.includes('authorized')) {
                                this.log('✅ 根据URL判断，验证可能已成功完成');
                                await this.takeScreenshot('STEP_11_navigation_success');
                                await this.saveHtmlContent('STEP_11_navigation_success');
                                return true; // 继续后续流程
                            }

                            isAutoSubmitPage = false;
                        }

                        if (isAutoSubmitPage) {
                            this.log('✅ 检测到自动提交验证页面，让其自然执行...');
                            this.log('🌿 不干预reCAPTCHA，让页面自动完成验证和提交');

                            // 等待页面自动执行验证和提交
                            this.log('⏳ 等待自动验证和提交完成...');
                            await this.wait(12000); // 给足够时间让页面自动处理

                            // 检查结果
                            const currentUrl = this.page.url();
                            this.log(`📍 自动提交后的页面: ${currentUrl}`);

                            // 检查页面内容判断结果
                            const pageResult = await this.page.evaluate(() => {
                                const bodyText = document.body.innerText.toLowerCase();
                                const bodyHtml = document.body.innerHTML.toLowerCase();

                                const hasFailure = ['rejected', 'failed', 'error', 'denied', 'blocked', 'try again'].some(
                                    indicator => bodyText.includes(indicator) || bodyHtml.includes(indicator)
                                );

                                const hasSuccess = ['success', 'complete', 'authorized', 'approved', 'verified', 'welcome', 'copy'].some(
                                    indicator => bodyText.includes(indicator) || bodyHtml.includes(indicator)
                                );

                                return {
                                    hasFailure,
                                    hasSuccess,
                                    bodyText: bodyText.substring(0, 300),
                                    url: window.location.href
                                };
                            });

                            this.log(`📄 页面结果分析: 失败=${pageResult.hasFailure}, 成功=${pageResult.hasSuccess}`);
                            this.log(`📝 页面内容: ${pageResult.bodyText}`);

                            if (pageResult.hasFailure) {
                                this.log('❌ STEP 11: 自动提交后检测到失败页面');
                                await this.takeScreenshot('STEP_11_natural_execution_failed');
                                await this.saveHtmlContent('STEP_11_natural_execution_failed');
                            } else if (pageResult.hasSuccess) {
                                this.log('🎉 STEP 11: 自动提交后检测到成功页面！');
                                await this.takeScreenshot('STEP_11_natural_execution_success');
                                await this.saveHtmlContent('STEP_11_natural_execution_success');
                            } else {
                                this.log('⚠️ STEP 11: 自动提交后无法确定结果');
                                await this.takeScreenshot('STEP_11_natural_execution_unknown');
                                await this.saveHtmlContent('STEP_11_natural_execution_unknown');
                            }
                        } else {
                            this.log('ℹ️ STEP 11: 非自动提交页面，继续正常流程...');

                            // Step 11 后的真人行为模拟 (5-8秒)
                            this.log('🤔 Step 11 后模拟真人思考和观察...');
                            const step11WaitTime = AntiFingerprint.randomBetween(5000, 8000);
                            this.log(`⏰ 等待 ${Math.round(step11WaitTime/1000)} 秒...`);
                            await this.wait(step11WaitTime);
                            await this.simulateRandomMouseMovement();
                            await this.simulateHesitation();
                        }
                    } else {
                        // 在按Enter键前，进行reCAPTCHA评分预热
                        this.log('🔥 开始reCAPTCHA评分预热 (Enter键版本)...');
                        await this.performRecaptchaWarmup();

                        this.log('未找到最终Continue按钮，尝试按Enter键');
                        await this.page.keyboard.press('Enter');
                        await this.wait(5000);
                        await this.takeScreenshot('final_enter_pressed');
                        await this.saveHtmlContent('final_enter_pressed');

                        // STEP 11: 检查并处理自动提交页面的reCAPTCHA (Enter键版本)
                        this.log('🔍 STEP 11: 检查是否为自动提交验证页面...');

                        // 等待页面稳定，避免在导航过程中检查
                        await this.wait(2000);

                        let isAutoSubmitPage = false;
                        try {
                            isAutoSubmitPage = await this.page.evaluate(() => {
                                // 检查页面是否还存在
                                if (!document || !document.body) {
                                    return false;
                                }

                                const bodyContent = document.body.innerHTML || '';
                                return bodyContent.includes('Auto-submit logic') ||
                                       bodyContent.includes('onClick') ||
                                       document.querySelector('#action-form') ||
                                       bodyContent.includes('grecaptcha.enterprise.execute') ||
                                       bodyContent.includes('Verifying you are human');
                            });
                        } catch (error) {
                            this.log(`⚠️ STEP 11: 页面检查失败 (可能已导航): ${error.message}`);
                            // 如果页面已经导航，检查当前URL是否表明成功
                            const currentUrl = this.page.url();
                            this.log(`📍 当前页面URL: ${currentUrl}`);

                            if (currentUrl.includes('success') || currentUrl.includes('complete') || currentUrl.includes('authorized')) {
                                this.log('✅ 根据URL判断，验证可能已成功完成');
                                await this.takeScreenshot('STEP_11_navigation_success_enter');
                                await this.saveHtmlContent('STEP_11_navigation_success_enter');
                                return true; // 继续后续流程
                            }

                            isAutoSubmitPage = false;
                        }

                        if (isAutoSubmitPage) {
                            this.log('✅ 检测到自动提交验证页面，让其自然执行...');
                            this.log('🌿 不干预reCAPTCHA，让页面自动完成验证和提交');

                            // 等待页面自动执行验证和提交
                            this.log('⏳ 等待自动验证和提交完成...');
                            await this.wait(12000); // 给足够时间让页面自动处理

                            // 检查结果
                            const currentUrl = this.page.url();
                            this.log(`📍 自动提交后的页面: ${currentUrl}`);

                            // 检查页面内容判断结果
                            const pageResult = await this.page.evaluate(() => {
                                const bodyText = document.body.innerText.toLowerCase();
                                const bodyHtml = document.body.innerHTML.toLowerCase();

                                const hasFailure = ['rejected', 'failed', 'error', 'denied', 'blocked', 'try again'].some(
                                    indicator => bodyText.includes(indicator) || bodyHtml.includes(indicator)
                                );

                                const hasSuccess = ['success', 'complete', 'authorized', 'approved', 'verified', 'welcome', 'copy'].some(
                                    indicator => bodyText.includes(indicator) || bodyHtml.includes(indicator)
                                );

                                return {
                                    hasFailure,
                                    hasSuccess,
                                    bodyText: bodyText.substring(0, 300),
                                    url: window.location.href
                                };
                            });

                            this.log(`📄 页面结果分析: 失败=${pageResult.hasFailure}, 成功=${pageResult.hasSuccess}`);
                            this.log(`📝 页面内容: ${pageResult.bodyText}`);

                            if (pageResult.hasFailure) {
                                this.log('❌ STEP 11: 自动提交后检测到失败页面');
                                await this.takeScreenshot('STEP_11_natural_execution_failed_enter');
                                await this.saveHtmlContent('STEP_11_natural_execution_failed_enter');
                            } else if (pageResult.hasSuccess) {
                                this.log('🎉 STEP 11: 自动提交后检测到成功页面！');
                                await this.takeScreenshot('STEP_11_natural_execution_success_enter');
                                await this.saveHtmlContent('STEP_11_natural_execution_success_enter');
                            } else {
                                this.log('⚠️ STEP 11: 自动提交后无法确定结果');
                                await this.takeScreenshot('STEP_11_natural_execution_unknown_enter');
                                await this.saveHtmlContent('STEP_11_natural_execution_unknown_enter');
                            }
                        } else {
                            this.log('ℹ️ STEP 11: 非自动提交页面，继续正常流程...');

                            // Step 11 后的真人行为模拟 (5-8秒) - Enter键版本
                            this.log('🤔 Step 11 后模拟真人思考和观察...');
                            const step11WaitTime = AntiFingerprint.randomBetween(5000, 8000);
                            this.log(`⏰ 等待 ${Math.round(step11WaitTime/1000)} 秒...`);
                            await this.wait(step11WaitTime);
                            await this.simulateRandomMouseMovement();
                            await this.simulateHesitation();
                        }
                    }

                    // 等待页面响应和可能的重定向
                    await this.wait(3000);

                    // 模拟验证成功后的自然反应（简化版）
                    this.log('✅ 模拟验证成功后的自然反应...');
                    await this.wait(AntiFingerprint.randomBetween(1000, 2000));
                    await this.simulateHesitation();

                    await this.takeScreenshot('verification_complete');
                    await this.saveHtmlContent('verification_complete');

                    // Step 12 后的真人行为模拟 (5-8秒)
                    this.log('🤔 Step 12 后模拟真人查看页面和思考...');
                    const step12WaitTime = AntiFingerprint.randomBetween(5000, 8000);
                    this.log(`⏰ 等待 ${Math.round(step12WaitTime/1000)} 秒...`);
                    await this.wait(step12WaitTime);
                    await this.simulateRandomMouseMovement();
                    await this.simulateHesitation();

                    // 点击 copy to clipboard 按钮并获取内容（快速失败版）
                    const clipboardContent = await this.clickCopyToClipboardAndGetContentFast();

                    // 如果成功获取到剪贴板内容，返回内容供后续处理
                    if (clipboardContent) {
                        this.log('✅ 邮箱验证流程完成，获取到剪贴板内容');
                        return clipboardContent;
                    }

                    // 未获取到剪贴板内容，直接宣告失败并抛错
                    throw new Error('验证失败：未获取到剪贴板内容');

                } else {
                    this.log('❌ 未找到验证码输入框');
                    await this.takeScreenshot('no_code_input', true);
                    await this.saveHtmlContent('no_code_input', true);
                    return null;
                }

            } else {
                this.log('❌ 未找到邮箱输入框');
                await this.takeScreenshot('no_email_input', true);
                await this.saveHtmlContent('no_email_input', true);
                return null;
            }

            return null;

        } catch (error) {
            this.log(`❌ 邮箱验证流程出错: ${error.message}`);

            // 检查是否是页面导航导致的错误
            if (error.message.includes('Execution context was destroyed') ||
                error.message.includes('Cannot read properties of null')) {
                this.log('⚠️ 检测到页面导航错误，尝试恢复...');

                try {
                    // 等待页面稳定
                    await this.wait(3000);

                    // 检查当前URL
                    const currentUrl = this.page.url();
                    this.log(`📍 当前页面URL: ${currentUrl}`);

                    // 如果URL表明成功，尝试获取剪贴板内容
                    if (currentUrl.includes('success') || currentUrl.includes('complete') ||
                        currentUrl.includes('authorized') || currentUrl.includes('code=')) {
                        this.log('✅ 根据URL判断可能已成功，尝试获取剪贴板内容...');

                        await this.takeScreenshot('recovery_attempt');
                        await this.saveHtmlContent('recovery_attempt');

                        // 尝试获取剪贴板内容
                        const clipboardContent = await this.getJsonData();
                        if (clipboardContent) {
                            this.log('🎉 恢复成功！获取到剪贴板内容');
                            return clipboardContent;
                        }
                    }
                } catch (recoveryError) {
                    this.log(`恢复尝试失败: ${recoveryError.message}`);
                }
            }

            await this.takeScreenshot('verification_error', true);
            await this.saveHtmlContent('verification_error', true);
            throw error;
        } finally {
            await this.cleanup();
        }
    }

    // 快速失败版本的 copy 按钮查找
    async clickCopyToClipboardAndGetContentFast() {
        try {
            // 简化的真人行为模拟
            this.log('🔍 查找 copy to clipboard 按钮...');
            await this.wait(AntiFingerprint.randomBetween(500, 1000));

            this.log('🔍 开始查找 copy 按钮...');

            // 设置剪贴板拦截
            await this.page.evaluate(() => {
                window.clipboardData = '';

                // 拦截 navigator.clipboard.writeText
                if (navigator.clipboard && navigator.clipboard.writeText) {
                    const originalWriteText = navigator.clipboard.writeText;
                    navigator.clipboard.writeText = function (text) {
                        window.clipboardData = text;
                        console.log('剪贴板内容已拦截:', text);
                        return originalWriteText.call(this, text);
                    };
                }

                // 拦截 document.execCommand('copy')
                const originalExecCommand = document.execCommand;
                document.execCommand = function (command, ...args) {
                    if (command === 'copy') {
                        const selection = window.getSelection();
                        if (selection.rangeCount > 0) {
                            window.clipboardData = selection.toString();
                            console.log('通过execCommand复制的内容:', window.clipboardData);
                        }
                    }
                    return originalExecCommand.call(this, command, ...args);
                };
            });

            // 快速查找copy按钮的关键选择器
            const copyButtonSelectors = [
                '#copyButton',  // 最常见的ID
                'button#copyButton',
                'button.copy-button',
                '.copy-button',
                'button[title*="copy"]',
                'button[aria-label*="copy"]'
            ];

            let copyButton = null;
            let foundSelector = null;

            // 快速查找，每个选择器最多等待1秒
            for (const selector of copyButtonSelectors) {
                try {
                    this.log(`尝试选择器: ${selector}`);
                    await this.page.waitForSelector(selector, { timeout: 1000 });
                    copyButton = await this.page.$(selector);
                    if (copyButton) {
                        foundSelector = selector;
                        this.log(`✅ 找到copy按钮: ${selector}`);
                        break;
                    }
                } catch (e) {
                    this.log(`选择器 ${selector} 未找到元素`);
                    continue;
                }
            }

            // 如果还没找到，快速尝试文本内容查找（最后一次机会）
            if (!copyButton) {
                this.log('最后尝试：通过文本内容查找copy按钮...');
                try {
                    copyButton = await this.page.evaluateHandle(() => {
                        const buttons = Array.from(document.querySelectorAll('button, [role="button"]'));
                        return buttons.find(btn => {
                            const text = btn.textContent || btn.innerText || '';
                            return text.toLowerCase().includes('copy');
                        });
                    });
                } catch (e) {
                    this.log('文本内容查找也失败');
                }
            }

            if (copyButton && copyButton.asElement()) {
                // 简化的点击前确认
                this.log('🎯 确认copy按钮位置...');
                await this.wait(AntiFingerprint.randomBetween(300, 600));

                this.log('🖱️ 点击 copy to clipboard 按钮...');
                await copyButton.click();
                await this.wait(2000); // 等待复制操作完成

                // 获取拦截到的剪贴板内容
                const clipboardContent = await this.page.evaluate(() => window.clipboardData);

                if (clipboardContent) {
                    this.logger.log('📋 检测到剪贴板内容');

                    // 保存到文件
                    const clipboardFile = path.join(this.imageDir, 'clipboard_content.txt');
                    fs.writeFileSync(clipboardFile, clipboardContent, 'utf8');
                    this.log(`💾 剪贴板内容已保存到: clipboard_content.txt`);

                    // 尝试解析为JSON
                    try {
                        const jsonData = JSON.parse(clipboardContent);
                        this.logger.log('🔐 剪贴板内容解析为JSON成功');

                        const jsonFile = path.join(this.imageDir, 'clipboard_content.json');
                        fs.writeFileSync(jsonFile, JSON.stringify(jsonData, null, 2), 'utf8');
                        this.log(`💾 JSON数据已保存到: clipboard_content.json`);
                    } catch (e) {
                        this.log('剪贴板内容不是有效的JSON格式');
                    }

                    await this.takeScreenshot('copy_button_clicked');
                    await this.saveHtmlContent('copy_button_clicked');

                    this.log('✅ 成功获取剪贴板内容，流程完成');
                    return clipboardContent;
                } else {
                    this.log('⚠️ 未获取到剪贴板内容');
                    await this.takeScreenshot('copy_button_clicked_no_content');
                    return null;
                }
            } else {
                this.log('❌ 未找到 copy to clipboard 按钮 - 验证流程失败');
                await this.takeScreenshot('no_copy_button_FAILED', true);
                await this.saveHtmlContent('no_copy_button_FAILED', true);

                // 快速失败 - 不再尝试其他方法
                this.log('💥 Copy按钮未找到，判定为验证失败，终止流程');
                throw new Error('验证流程失败：未找到copy按钮，可能被反自动化系统检测');
            }

        } catch (error) {
            this.log(`❌ 点击copy按钮时出错: ${error.message}`);
            await this.takeScreenshot('copy_button_error', true);
            throw error;
        }
    }

    // 设置网络监听
    async setupNetworkMonitoring() {
        try {
            this.log('🌐 设置网络监听...');

            // 监听所有网络请求
            await this.page.setRequestInterception(true);

            this.page.on('request', (request) => {
                const url = request.url();

                // 记录授权相关的请求
                if (url.includes('authorize') || url.includes('token') || url.includes('oauth') ||
                    url.includes('auth') || url.includes('login') || url.includes('callback')) {
                    const fullMessage = `📤 授权相关请求: ${request.method()} ${url}`;
                    this.logger.logNetwork('request', fullMessage);
                }

                // 检查请求是否已经被处理
                if (!request.isInterceptResolutionHandled()) {
                    request.continue();
                }
            });

            this.page.on('response', async (response) => {
                const url = response.url();
                const status = response.status();

                // 记录授权相关的响应
                if (url.includes('authorize') || url.includes('token') || url.includes('oauth') ||
                    url.includes('auth') || url.includes('login') || url.includes('callback')) {
                    const fullMessage = `📥 授权相关响应: ${status} ${url}`;
                    this.logger.logNetwork('response', fullMessage);

                    // 如果是重定向响应，记录Location头
                    if (status >= 300 && status < 400) {
                        const location = response.headers()['location'];
                        if (location) {
                            const fullMessage = `🔄 重定向到: ${location}`;
                            this.logger.logNetwork('redirect', fullMessage);

                            // 检查重定向URL是否包含授权码
                            if (location.includes('code=') || location.includes('access_token=')) {
                                this.logger.logNetwork('auth_success', '🎉 在重定向中检测到授权响应', null, { redirectUrl: location });

                                // 解析重定向URL中的参数
                                try {
                                    const redirectUrl = new URL(location);
                                    const authParams = {};

                                    ['code', 'access_token', 'state', 'error', 'error_description'].forEach(param => {
                                        const value = redirectUrl.searchParams.get(param);
                                        if (value) {
                                            authParams[param] = value;
                                        }
                                    });

                                    // 保存详细参数到日志文件，控制台只显示简要信息
                                    this.logger.saveDebugInfo('authorization_params.json', authParams);

                                    // 保存重定向响应
                                    const redirectData = {
                                        timestamp: new Date().toISOString(),
                                        originalUrl: url,
                                        redirectUrl: location,
                                        authParams: authParams,
                                        status: status
                                    };

                                    const redirectFile = path.join(this.imageDir, 'authorization_redirect.json');
                                    fs.writeFileSync(redirectFile, JSON.stringify(redirectData, null, 2), 'utf8');
                                    this.log(`💾 重定向授权响应已保存到: authorization_redirect.json`);
                                } catch (e) {
                                    this.log(`解析重定向URL失败: ${e.message}`);
                                }
                            }
                        }
                    }

                    // 尝试获取响应体（如果是JSON）
                    try {
                        const contentType = response.headers()['content-type'] || '';
                        if (contentType.includes('application/json')) {
                            const responseBody = await response.text();
                            if (responseBody && (responseBody.includes('authorization_code') ||
                                responseBody.includes('access_token') || responseBody.includes('code'))) {
                                this.logger.logNetwork('auth_json', '🔐 检测到授权相关JSON响应', null, { url, responseBody });

                                const jsonResponseFile = path.join(this.imageDir, 'authorization_json_response.json');
                                const jsonData = {
                                    timestamp: new Date().toISOString(),
                                    url: url,
                                    status: status,
                                    responseBody: responseBody
                                };
                                fs.writeFileSync(jsonResponseFile, JSON.stringify(jsonData, null, 2), 'utf8');
                                this.log(`💾 JSON授权响应已保存到: authorization_json_response.json`);
                            }
                        }
                    } catch (e) {
                        // 忽略响应体读取错误
                    }
                }
            });

            this.log('✅ 网络监听设置完成');

        } catch (error) {
            this.log(`❌ 设置网络监听失败: ${error.message}`);
        }
    }

    // 捕获授权响应
    async captureAuthorizationResponse() {
        try {
            this.log('🔍 开始监听授权响应...');

            // 等待页面可能的跳转或响应
            let maxWaitTime = 30000; // 30秒超时
            let checkInterval = 1000; // 每秒检查一次
            let elapsed = 0;

            while (elapsed < maxWaitTime) {
                const currentUrl = this.page.url();
                this.log(`当前URL: ${currentUrl}`);

                // 检查URL是否包含授权响应参数
                if (currentUrl.includes('code=') || currentUrl.includes('access_token=') || currentUrl.includes('error=')) {
                    this.log('🎉 检测到授权响应！');
                    await this.takeScreenshot('authorization_response_detected');
                    await this.saveHtmlContent('authorization_response_detected');

                    // 解析URL参数
                    const urlParams = new URL(currentUrl);
                    const authResponse = {};

                    // 提取常见的OAuth参数
                    const params = ['code', 'access_token', 'token_type', 'expires_in', 'scope', 'state', 'error', 'error_description'];
                    params.forEach(param => {
                        const value = urlParams.searchParams.get(param);
                        if (value) {
                            authResponse[param] = value;
                        }
                    });

                    // 如果URL fragment中有参数（用于implicit flow）
                    if (urlParams.hash) {
                        const hashParams = new URLSearchParams(urlParams.hash.substring(1));
                        params.forEach(param => {
                            const value = hashParams.get(param);
                            if (value) {
                                authResponse[param] = value;
                            }
                        });
                    }

                    // 使用 logger 记录详细信息到文件，控制台显示简要信息
                    this.logger.logNetwork('auth_success', '🔐 检测到授权响应', null, {
                        fullUrl: currentUrl,
                        parsedParams: authResponse
                    });

                    // 保存到文件
                    const responseData = {
                        timestamp: new Date().toISOString(),
                        fullUrl: currentUrl,
                        parsedParams: authResponse,
                        pageTitle: await this.page.title()
                    };

                    const responseFile = path.join(this.imageDir, 'authorization_response.json');
                    fs.writeFileSync(responseFile, JSON.stringify(responseData, null, 2), 'utf8');
                    this.log(`💾 授权响应已保存到: authorization_response.json`);

                    return authResponse;
                }

                // 检查页面内容是否包含授权相关信息
                const pageContent = await this.page.content();
                if (pageContent.includes('authorization_code') || pageContent.includes('access_token') ||
                    pageContent.includes('Authorization successful') || pageContent.includes('授权成功')) {
                    this.log('🎉 在页面内容中检测到授权信息！');
                    await this.takeScreenshot('authorization_content_detected');
                    await this.saveHtmlContent('authorization_content_detected');

                    // 尝试从页面中提取授权信息
                    const authInfo = await this.page.evaluate(() => {
                        const result = {};

                        // 查找包含授权码的元素
                        const codeElements = document.querySelectorAll('*');
                        for (let element of codeElements) {
                            const text = element.textContent || '';
                            if (text.includes('code:') || text.includes('authorization_code:')) {
                                result.pageContent = text;
                                break;
                            }
                        }

                        // 查找可能的JSON数据
                        const scripts = document.querySelectorAll('script');
                        for (let script of scripts) {
                            const content = script.textContent || '';
                            if (content.includes('authorization_code') || content.includes('access_token')) {
                                try {
                                    const match = content.match(/\{[^}]*(?:authorization_code|access_token)[^}]*\}/);
                                    if (match) {
                                        result.jsonData = JSON.parse(match[0]);
                                    }
                                } catch (e) {
                                    result.scriptContent = content;
                                }
                                break;
                            }
                        }

                        return result;
                    });

                    if (Object.keys(authInfo).length > 0) {
                        this.logger.log('🔐 在页面中找到授权信息');

                        const responseFile = path.join(this.imageDir, 'authorization_page_content.json');
                        fs.writeFileSync(responseFile, JSON.stringify(authInfo, null, 2), 'utf8');
                        this.log(`💾 页面授权信息已保存到: authorization_page_content.json`);
                    }

                    return authInfo;
                }

                await this.wait(checkInterval);
                elapsed += checkInterval;
            }

            this.log('⏰ 授权响应监听超时，未检测到授权响应');
            return null;

        } catch (error) {
            this.log(`❌ 捕获授权响应时出错: ${error.message}`);
            return null;
        }
    }
}

async function main() {
    const autoRegister = new AutoRegister();

    try {
        console.log('🚀 Starting email verification process with One Mail API');

        // 使用你提供的URL，这里需要替换为实际的验证页面URL
        const verificationUrl = 'https://www.augmentcode.com/'; // 请替换为实际的邮箱验证页面URL

        await autoRegister.handleEmailVerificationWithOneMailAPI(verificationUrl);
        console.log('🎉 Email verification process completed successfully.');

    } catch (error) {
        console.error('💥 Process failed:', error.message);
        process.exit(1);
    }
}

if (require.main === module) {
    main();
}

module.exports = AutoRegister;

